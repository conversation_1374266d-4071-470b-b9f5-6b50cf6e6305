import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ModalFooter,
  Select,
  SelectItem,
  Input,
  Button,
  DatePicker,
} from "@heroui/react";

import { Project } from "@/types/projects";
import { useProjectDates } from "@/hooks/projects/useProjectDates";

interface EditModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedHistory: number | null;
  selectedProject: Project | null;
  phase: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const EditModal = ({
  isOpen,
  onClose,
  selectedHistory: _selectedHistory,
  selectedProject,
  phase,
  onSuccess,
  onError,
}: EditModalProps) => {
  const [selectedDateType, setSelectedDateType] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<any | null>(null);
  const [reason, setReason] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  const { updateProjectDates, fetchDateFields, dateFields, loading, error } =
    useProjectDates();

  // Fetch date fields when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchDateFields();
    }
  }, [isOpen]);

  // Helper function to map phase string to phase ID
  const getPhaseId = (phaseString: string): number => {
    const phaseMap: { [key: string]: number } = {
      Start: 1,
      Collection: 2,
      Migration: 3,
      Test: 4,
      "Go Live": 5,
      Incubadora: 6,
    };

    return phaseMap[phaseString] || 1;
  };

  const onModalSave = async () => {
    if (
      !selectedProject ||
      !selectedDateType ||
      !selectedDate ||
      !reason.trim()
    ) {
      onError?.("Por favor complete todos los campos requeridos");

      return;
    }

    try {
      setIsSubmitting(true);

      // Convert HeroUI DatePicker format to YYYY-MM-DD string format
      const formattedDate = `${selectedDate.year}-${String(selectedDate.month).padStart(2, "0")}-${String(selectedDate.day).padStart(2, "0")}`;

      const projectId = parseInt(selectedProject.id);
      const phaseId = getPhaseId(phase);

      await updateProjectDates(
        projectId,
        selectedDateType,
        formattedDate,
        phaseId,
        reason,
      );

      onSuccess?.();
      onClose();

      // Reset form
      setSelectedDateType(null);
      setSelectedDate(null);
      setReason("");
    } catch {
      onError?.(error || "Error al actualizar la fecha del proyecto");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Modal
        isDismissable={false}
        isKeyboardDismissDisabled={true}
        isOpen={isOpen}
        onClose={onClose}
      >
        <ModalContent>
          {() => (
            <>
              <ModalHeader>Actualizar fecha</ModalHeader>
              <ModalBody>
                <Select
                  classNames={{
                    base: "w-full",
                  }}
                  isLoading={loading}
                  label="Seleccionar fecha a cambiar"
                  labelPlacement="inside"
                  placeholder="Seleccionar fecha a cambiar"
                  selectedKeys={selectedDateType ? [selectedDateType] : []}
                  onChange={(e) => {
                    setSelectedDateType(e.target.value);
                  }}
                >
                  {dateFields.map((field) => (
                    <SelectItem key={field.name}>{field.name}</SelectItem>
                  ))}
                </Select>
                <DatePicker
                  className="w-full"
                  label="Seleccionar nueva fecha"
                  value={selectedDate}
                  onChange={(date) => {
                    setSelectedDate(date);
                  }}
                />
                <Input
                  className="w-full"
                  label="Razón del cambio"
                  placeholder="Ingrese la razón del cambio"
                  value={reason}
                  onChange={(e) => {
                    setReason(e.target.value);
                  }}
                />
              </ModalBody>
              <ModalFooter className="flex justify-between">
                <Button
                  color="danger"
                  isDisabled={isSubmitting}
                  variant="light"
                  onPress={onClose}
                >
                  Cerrar
                </Button>
                <Button
                  color="primary"
                  isDisabled={
                    !selectedProject ||
                    !selectedDateType ||
                    !selectedDate ||
                    !reason.trim()
                  }
                  isLoading={isSubmitting}
                  onPress={onModalSave}
                >
                  Guardar
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};
