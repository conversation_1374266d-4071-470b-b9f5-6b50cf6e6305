import React, { useMemo, useState } from "react";
import {
  Input,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Pa<PERSON>ation,
  Spin<PERSON>,
  Card,
  CardBody,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useQuery } from "@apollo/client";

import { FilterDropdown } from "@/components/projects/projects-table/filter-dropdown";
import {
  AllNotificationsDocument,
  NotificationType,
} from "@/graphql/schemas/generated";

interface SelectableNotificationsTableProps {
  selectedNotifications: string[];
  onSelectionChange: (selectedNotifications: string[]) => void;
}

export default function SelectableNotificationsTable({
  selectedNotifications,
  onSelectionChange,
}: SelectableNotificationsTableProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(1);
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [sortConfig, setSortConfig] = useState<{
    column: string;
    direction: "asc" | "desc";
  } | null>(null);

  const rowsPerPage = 10;

  const { data, loading, error } = useQuery(AllNotificationsDocument);

  const notifications = data?.allNotifications || [];

  const filteredAndSortedNotifications = useMemo(() => {
    let filtered = notifications.filter((notification: NotificationType) => {
      if (!notification) return false;

      // Search filter
      const searchMatch =
        notification.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        notification.description
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase());

      return searchMatch;
    });

    // Sort
    if (sortConfig) {
      filtered.sort((a: NotificationType, b: NotificationType) => {
        let aValue = "";
        let bValue = "";

        switch (sortConfig.column) {
          case "name":
            aValue = a.name || "";
            bValue = b.name || "";
            break;
          case "description":
            aValue = a.description || "";
            bValue = b.description || "";
            break;
          default:
            return 0;
        }

        const comparison = aValue.localeCompare(bValue);

        return sortConfig.direction === "asc" ? comparison : -comparison;
      });
    }

    return filtered;
  }, [notifications, searchTerm, sortConfig]);

  const pages = Math.ceil(filteredAndSortedNotifications.length / rowsPerPage);
  const items = useMemo(() => {
    const start = (page - 1) * rowsPerPage;
    const end = start + rowsPerPage;

    return filteredAndSortedNotifications.slice(start, end);
  }, [page, filteredAndSortedNotifications]);

  const handleSelectionChange = (keys: any) => {
    if (keys === "all") {
      const allKeys = filteredAndSortedNotifications.map(
        (notification: NotificationType) => notification.id,
      );

      onSelectionChange(allKeys);
    } else {
      const newSelection = Array.from(keys).map((key) => String(key));

      onSelectionChange(newSelection);
    }
  };

  const handleFilterChange = (column: string, values: string[]) => {
    setActiveFilters((prev) => ({ ...prev, [column]: values }));
    setPage(1);
  };

  const handleSort = (column: string, direction: "asc" | "desc") => {
    setSortConfig({ column, direction });
  };

  if (loading) {
    return (
      <Card>
        <CardBody className="flex items-center justify-center py-10">
          <Spinner label="Cargando notificaciones..." />
        </CardBody>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardBody className="flex items-center justify-center py-10">
          <p className="text-danger">
            Error al cargar las notificaciones: {error.message}
          </p>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search */}
      <Input
        isClearable
        placeholder="Buscar notificaciones..."
        startContent={<Icon icon="heroicons:magnifying-glass" />}
        value={searchTerm}
        onClear={() => setSearchTerm("")}
        onValueChange={setSearchTerm}
      />

      {/* Selected count */}
      {selectedNotifications.length > 0 && (
        <Card className="bg-primary-50 dark:bg-primary-900">
          <CardBody className="py-2">
            <p className="text-primary-600 dark:text-primary-300 text-sm">
              {selectedNotifications.length} notificación(es) seleccionada(s)
            </p>
          </CardBody>
        </Card>
      )}

      {/* Table */}
      <Table
        removeWrapper
        aria-label="Tabla de notificaciones seleccionables"
        bottomContent={
          pages > 1 ? (
            <div className="flex w-full justify-center">
              <Pagination
                isCompact
                showControls
                showShadow
                color="primary"
                page={page}
                total={pages}
                onChange={setPage}
              />
            </div>
          ) : null
        }
        selectedKeys={new Set(selectedNotifications)}
        selectionMode="multiple"
        onSelectionChange={handleSelectionChange}
      >
        <TableHeader>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="name"
              items={[]}
              sortConfig={sortConfig}
              title="Nombre"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="description"
              items={[]}
              sortConfig={sortConfig}
              title="Descripción"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>ID</TableColumn>
        </TableHeader>
        <TableBody
          emptyContent="No hay notificaciones disponibles"
          items={items}
        >
          {(item: NotificationType) => (
            <TableRow key={item.id}>
              <TableCell>
                <div className="font-medium">{item.name}</div>
              </TableCell>
              <TableCell>
                <div className="max-w-md">
                  <p className="text-sm text-default-600 line-clamp-3">
                    {item.description || "Sin descripción"}
                  </p>
                </div>
              </TableCell>
              <TableCell>
                <span className="text-xs text-default-400 font-mono">
                  {item.id}
                </span>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
