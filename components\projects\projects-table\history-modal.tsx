"use client";
import type { SortDescriptor } from "@heroui/system-rsc/node_modules/@react-types/shared";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TableCell,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
} from "@nextui-org/react";
import { Button } from "@heroui/button";
import { useState, useEffect } from "react";

import { getPhaseStyleText } from "@/components/primitives";

interface HistoryItem {
  id: number;
  user: string;
  phase: string;
  type: string;
  modificationDate: string;
  value: string;
  comment: string;
}

interface HistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedProject: number | null;
}

export const HistoryModal = ({
  isOpen,
  onClose,
  selectedProject,
}: HistoryModalProps) => {
  const [historyData, setHistoryData] = useState<HistoryItem[]>([]);
  const [sortDescriptor, setSortDescriptor] = useState<SortDescriptor>({
    column: "modificationDate" as string,
    direction: "descending",
  });

  useEffect(() => {
    if (isOpen) {
      // Generate dummy data when modal is opened
      const dummyData: HistoryItem[] = [
        {
          id: 1,
          user: "Ana López",
          phase: "START",
          type: "Inicio previsto",
          modificationDate: "2023-12-10",
          value: "2024-01-15",
          comment: "Inicio de la fase de recolección",
        },
        {
          id: 2,
          user: "Carlos Martínez",
          phase: "COLLECTION",
          type: "Inicio real",
          modificationDate: "2023-12-15",
          value: "2024-02-20",
          comment: "Inicio de la fase de recolección",
        },
        {
          id: 3,
          user: "Laura García",
          phase: "START",
          type: "Fin real",
          modificationDate: "2023-11-30",
          value: "2024-01-10",
          comment: "Fin de la fase de recolección",
        },
        {
          id: 4,
          user: "Roberto Sánchez",
          phase: "MIGRATION",
          type: "Fin real",
          modificationDate: "2023-12-05",
          value: "2024-03-01",
          comment: "Fin de la fase de recolección",
        },
        {
          id: 5,
          user: "María Rodríguez",
          phase: "COLLECTION",
          type: "Fin previsto",
          modificationDate: "2023-12-20",
          value: "2024-02-15",
          comment: "Fin de la fase de recolección",
        },
      ];

      setHistoryData(dummyData);
    }
  }, [isOpen, selectedProject]);

  const sortedItems = [...historyData].sort((a, b) => {
    const first = a[sortDescriptor.column as keyof HistoryItem] as string;
    const second = b[sortDescriptor.column as keyof HistoryItem] as string;
    const cmp = first.localeCompare(second);

    return sortDescriptor.direction === "descending" ? -cmp : cmp;
  });

  const handleSortChange = (descriptor: SortDescriptor) => {
    setSortDescriptor(descriptor);
  };

  return (
    <>
      <Modal
        isDismissable={false}
        isKeyboardDismissDisabled={true}
        isOpen={isOpen}
        size="4xl"
        onClose={onClose}
      >
        <ModalContent>
          {(onModalClose) => (
            <>
              <ModalHeader>Historial</ModalHeader>
              <ModalBody>
                <Table
                  removeWrapper
                  sortDescriptor={sortDescriptor}
                  onSortChange={handleSortChange}
                >
                  <TableHeader>
                    <TableColumn key="timestamp" allowsSorting>
                      Timestamp
                    </TableColumn>
                    <TableColumn key="phase" allowsSorting>
                      Fase
                    </TableColumn>
                    <TableColumn key="type" allowsSorting>
                      Tipo
                    </TableColumn>
                    <TableColumn key="value" allowsSorting>
                      Valor
                    </TableColumn>
                    <TableColumn key="user" allowsSorting>
                      Usuario
                    </TableColumn>
                    <TableColumn key="comment" allowsSorting>
                      Comentario
                    </TableColumn>
                  </TableHeader>
                  <TableBody items={sortedItems}>
                    {(item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          {new Date(item.modificationDate).toLocaleDateString()}
                        </TableCell>
                        <TableCell className={getPhaseStyleText(item.phase)}>
                          {item.phase}
                        </TableCell>
                        <TableCell>{item.type}</TableCell>
                        <TableCell>
                          {new Date(item.value).toLocaleDateString()}
                        </TableCell>
                        <TableCell>{item.user}</TableCell>
                        <TableCell>{item.comment}</TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </ModalBody>
              <ModalFooter>
                <Button color="primary" onPress={onModalClose}>
                  Cerrar
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};
